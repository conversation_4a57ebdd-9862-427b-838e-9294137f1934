<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Last Purchase/Sale Report - Print</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />
  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    @import url('https://rsms.me/inter/inter.css');

    body {
      font-family: 'Inter', sans-serif;
      padding: 40px;
      background: #fff;
      color: #000;
    }

    .report-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    /* Company Header */
    .company-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #000;
      padding-bottom: 20px;
    }

    .company-name {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
      color: #000;
    }

    .company-address {
      font-size: 14px;
      color: #666;
      margin-bottom: 10px;
    }

    .report-title {
      font-size: 24px;
      font-weight: bold;
      color: #000;
      margin-top: 15px;
    }

    /* Report Info */
    .report-info {
      margin-bottom: 30px;
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .report-info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
    }

    .info-section h6 {
      font-weight: bold;
      margin-bottom: 10px;
      color: #495057;
      border-bottom: 1px solid #dee2e6;
      padding-bottom: 5px;
    }

    .info-item {
      margin-bottom: 6px;
      font-size: 13px;
    }

    .info-label {
      font-weight: bold;
      display: inline-block;
      width: 120px;
    }

    /* Table Styles */
    .report-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
      font-size: 11px;
    }

    .report-table th {
      background-color: #f8f9fa;
      border: 1px solid #000;
      padding: 8px 6px;
      text-align: center;
      font-weight: bold;
      font-size: 10px;
    }

    .report-table td {
      border: 1px solid #000;
      padding: 6px;
      text-align: center;
      font-size: 10px;
    }

    .report-table tbody tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .text-left {
      text-align: left !important;
    }

    .text-right {
      text-align: right !important;
    }

    /* Badge Styles */
    .badge {
      padding: 3px 6px;
      border-radius: 3px;
      font-size: 9px;
      font-weight: bold;
    }

    .badge-success {
      background-color: #28a745;
      color: white;
    }

    .badge-info {
      background-color: #17a2b8;
      color: white;
    }

    /* Summary Section */
    .report-summary {
      margin-top: 30px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .summary-card {
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .summary-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #495057;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 14px;
    }

    .summary-total {
      border-top: 2px solid #495057;
      padding-top: 8px;
      margin-top: 8px;
      font-weight: bold;
    }

    /* Print Styles */
    @media print {
      body {
        padding: 20px;
        font-size: 11px;
      }
      
      .no-print {
        display: none !important;
      }
      
      .report-table {
        font-size: 9px;
      }
      
      .report-table th, .report-table td {
        padding: 4px 3px;
        font-size: 9px;
      }
      
      .company-name {
        font-size: 24px;
      }
      
      .report-title {
        font-size: 20px;
      }

      .report-info-grid {
        grid-template-columns: 1fr 1fr;
      }
    }

    /* Print Button */
    .print-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }

    .btn {
      margin-left: 10px;
    }
  </style>
</head>
<body>

  <!-- Print Controls -->
  <div class="print-controls no-print">
    <button class="btn btn-primary" onclick="window.print()">🖨️ Print Report</button>
    <button class="btn btn-secondary" onclick="window.close()">✕ Close</button>
  </div>

  <div class="report-container">
    <!-- Company Header -->
    <div class="company-header" id="companyHeader">
      <div class="company-name">Hzins International Pvt Ltd</div>
      <div class="company-address">
        123 Business District, Karachi, Pakistan<br>
        Phone: +92-21-1234567 | Email: <EMAIL>
      </div>
      <div class="report-title" id="reportTitle">Last Transaction Report</div>
    </div>

    <!-- Report Information -->
    <div class="report-info">
      <div class="report-info-grid">
        <div class="info-section">
          <h6>Filter Information</h6>
          <div class="info-item">
            <span class="info-label">Product:</span>
            <span id="reportProduct">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Transaction Type:</span>
            <span id="reportTransactionType">-</span>
          </div>
        </div>
        <div class="info-section">
          <h6>Report Details</h6>
          <div class="info-item">
            <span class="info-label">Generated:</span>
            <span id="reportDate">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Generated By:</span>
            <span>Admin User</span>
          </div>
        </div>
        <div class="info-section">
          <h6>Summary</h6>
          <div class="info-item">
            <span class="info-label">Total Records:</span>
            <span id="totalRecords">0</span>
          </div>
          <div class="info-item">
            <span class="info-label">Total Value:</span>
            <span id="reportTotalValue">Rs. 0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction Table -->
    <table class="report-table" id="reportTable">
      <thead id="tableHead">
        <!-- Headers will be populated by JavaScript -->
      </thead>
      <tbody id="reportTableBody">
        <!-- Data will be populated by JavaScript -->
      </tbody>
    </table>

    <!-- Report Summary -->
    <div class="report-summary">
      <div class="summary-card">
        <div class="summary-title">Transaction Summary</div>
        <div class="summary-item">
          <span>Total Products:</span>
          <span id="summaryTotalProducts">0</span>
        </div>
        <div class="summary-item">
          <span>Total Transactions:</span>
          <span id="summaryTotalTransactions">0</span>
        </div>
        <div class="summary-item">
          <span>Average Transaction Value:</span>
          <span id="avgTransactionValue">Rs. 0</span>
        </div>
      </div>
      <div class="summary-card">
        <div class="summary-title">Financial Summary</div>
        <div class="summary-item">
          <span>Total Quantity:</span>
          <span id="totalQuantity">0</span>
        </div>
        <div class="summary-item">
          <span>Average Unit Price:</span>
          <span id="avgUnitPrice">Rs. 0</span>
        </div>
        <div class="summary-item summary-total">
          <span>Grand Total:</span>
          <span id="grandTotal">Rs. 0</span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
      <p>This is a computer generated report. No signature required.</p>
      <p>Generated on <span id="footerDate"></span> | Page 1 of 1</p>
    </div>
  </div>

  <script>
    // Sample transaction data (same as main page)
    const sampleTransactionData = {
      lastSale: [
        { productCode: 'ITM001', productName: 'Wireless Mouse', customer: 'ABC Traders', salesman: 'Ahmed Ali', qty: 10, unitPrice: 1500, transactionDate: '2024-01-22', invoiceNo: 'INV-2024-001' },
        { productCode: 'ITM002', productName: 'Keyboard', customer: 'XYZ Enterprises', salesman: 'Muhammad Hassan', qty: 12, unitPrice: 2500, transactionDate: '2024-01-21', invoiceNo: 'INV-2024-002' },
        { productCode: 'ITM003', productName: 'Monitor 24"', customer: 'Global Solutions', salesman: 'Fatima Khan', qty: 3, unitPrice: 25000, transactionDate: '2024-01-20', invoiceNo: 'INV-2024-003' },
        { productCode: 'ITM004', productName: 'USB Cable', customer: 'Tech World', salesman: 'Ali Raza', qty: 50, unitPrice: 300, transactionDate: '2024-01-19', invoiceNo: 'INV-2024-004' },
        { productCode: 'ITM005', productName: 'Hard Drive 1TB', customer: 'Prime Electronics', salesman: 'Sara Ahmed', qty: 8, unitPrice: 8000, transactionDate: '2024-01-18', invoiceNo: 'INV-2024-005' }
      ],
      lastPurchase: [
        { productCode: 'ITM001', productName: 'Wireless Mouse', supplier: 'Tech Suppliers Ltd', purchaseOfficer: 'Usman Khan', qty: 100, unitPrice: 1200, transactionDate: '2024-01-15', poNumber: 'PO-2024-001' },
        { productCode: 'ITM002', productName: 'Keyboard', supplier: 'Global Tech Inc', purchaseOfficer: 'Ayesha Shah', qty: 50, unitPrice: 2000, transactionDate: '2024-01-14', poNumber: 'PO-2024-002' },
        { productCode: 'ITM003', productName: 'Monitor 24"', supplier: 'Display Solutions', purchaseOfficer: 'Hassan Ali', qty: 20, unitPrice: 22000, transactionDate: '2024-01-13', poNumber: 'PO-2024-003' },
        { productCode: 'ITM004', productName: 'USB Cable', supplier: 'Cable World', purchaseOfficer: 'Fatima Ahmed', qty: 200, unitPrice: 250, transactionDate: '2024-01-12', poNumber: 'PO-2024-004' },
        { productCode: 'ITM005', productName: 'Hard Drive 1TB', supplier: 'Storage Plus', purchaseOfficer: 'Ali Hassan', qty: 30, unitPrice: 7200, transactionDate: '2024-01-11', poNumber: 'PO-2024-005' }
      ]
    };

    // Get URL parameters
    function getUrlParams() {
      const params = new URLSearchParams(window.location.search);
      return {
        product: params.get('product') || '',
        allProducts: params.get('allProducts') === 'true',
        transactionType: params.get('transactionType') || 'last_sale',
        showHeading: params.get('showHeading') === 'true',
        print: params.get('print') === 'true'
      };
    }

    // Initialize report
    function initializeReport() {
      const params = getUrlParams();
      
      // Set current date
      const currentDate = new Date().toLocaleDateString('en-GB');
      document.getElementById('reportDate').textContent = currentDate;
      document.getElementById('footerDate').textContent = currentDate;
      
      // Set report title and transaction type
      const transactionTypeLabels = {
        'last_sale': 'Last Sale Report',
        'last_purchase': 'Last Purchase Report'
      };
      
      document.getElementById('reportTitle').textContent = transactionTypeLabels[params.transactionType];
      document.getElementById('reportTransactionType').textContent = transactionTypeLabels[params.transactionType];
      
      // Set product information
      if (params.allProducts) {
        document.getElementById('reportProduct').textContent = 'All Products';
      } else {
        const productOptions = {
          'ITM001': 'ITM001 - Wireless Mouse',
          'ITM002': 'ITM002 - Keyboard',
          'ITM003': 'ITM003 - Monitor 24"',
          'ITM004': 'ITM004 - USB Cable',
          'ITM005': 'ITM005 - Hard Drive 1TB'
        };
        document.getElementById('reportProduct').textContent = productOptions[params.product] || params.product || 'All Products';
      }
      
      // Show/hide company header
      if (!params.showHeading) {
        document.getElementById('companyHeader').style.display = 'none';
      }
      
      // Populate table
      populateTable(params);
      
      // Auto print if requested
      if (params.print) {
        setTimeout(() => {
          window.print();
        }, 1000);
      }
    }

    // Populate table with data
    function populateTable(params) {
      // Get data based on transaction type
      let data = sampleTransactionData[params.transactionType];
      
      // Filter by product if specific product is selected
      if (!params.allProducts && params.product) {
        data = data.filter(item => item.productCode === params.product);
      }

      // Set table headers
      const tableHead = document.getElementById('tableHead');
      if (params.transactionType === 'last_sale') {
        tableHead.innerHTML = `
          <tr>
            <th style="width: 5%;">S.No</th>
            <th style="width: 10%;">Product Code</th>
            <th style="width: 20%;">Product Name</th>
            <th style="width: 15%;">Customer</th>
            <th style="width: 12%;">Salesman</th>
            <th style="width: 8%;">Qty</th>
            <th style="width: 10%;">Unit Price</th>
            <th style="width: 12%;">Total Amount</th>
            <th style="width: 8%;">Date</th>
            <th style="width: 10%;">Invoice No</th>
          </tr>
        `;
      } else {
        tableHead.innerHTML = `
          <tr>
            <th style="width: 5%;">S.No</th>
            <th style="width: 10%;">Product Code</th>
            <th style="width: 20%;">Product Name</th>
            <th style="width: 15%;">Supplier</th>
            <th style="width: 12%;">Purchase Officer</th>
            <th style="width: 8%;">Qty</th>
            <th style="width: 10%;">Unit Price</th>
            <th style="width: 12%;">Total Amount</th>
            <th style="width: 8%;">Date</th>
            <th style="width: 10%;">PO Number</th>
          </tr>
        `;
      }

      const tableBody = document.getElementById('reportTableBody');
      let totalQuantity = 0;
      let totalAmount = 0;
      let totalProducts = data.length;
      
      data.forEach((transaction, index) => {
        const row = document.createElement('tr');
        const amount = transaction.qty * transaction.unitPrice;
        
        totalQuantity += transaction.qty;
        totalAmount += amount;
        
        if (params.transactionType === 'last_sale') {
          row.innerHTML = `
            <td>${index + 1}</td>
            <td>${transaction.productCode}</td>
            <td class="text-left">${transaction.productName}</td>
            <td class="text-left">${transaction.customer}</td>
            <td class="text-left">${transaction.salesman}</td>
            <td>${transaction.qty}</td>
            <td class="text-right">Rs. ${transaction.unitPrice.toLocaleString()}</td>
            <td class="text-right">Rs. ${amount.toLocaleString()}</td>
            <td>${new Date(transaction.transactionDate).toLocaleDateString('en-GB')}</td>
            <td><span class="badge badge-success">${transaction.invoiceNo}</span></td>
          `;
        } else {
          row.innerHTML = `
            <td>${index + 1}</td>
            <td>${transaction.productCode}</td>
            <td class="text-left">${transaction.productName}</td>
            <td class="text-left">${transaction.supplier}</td>
            <td class="text-left">${transaction.purchaseOfficer}</td>
            <td>${transaction.qty}</td>
            <td class="text-right">Rs. ${transaction.unitPrice.toLocaleString()}</td>
            <td class="text-right">Rs. ${amount.toLocaleString()}</td>
            <td>${new Date(transaction.transactionDate).toLocaleDateString('en-GB')}</td>
            <td><span class="badge badge-info">${transaction.poNumber}</span></td>
          `;
        }
        
        tableBody.appendChild(row);
      });
      
      // Calculate averages
      const avgTransactionValue = totalProducts > 0 ? Math.round(totalAmount / totalProducts) : 0;
      const avgUnitPrice = totalQuantity > 0 ? Math.round(totalAmount / totalQuantity) : 0;
      
      // Update summary
      document.getElementById('totalRecords').textContent = totalProducts;
      document.getElementById('reportTotalValue').textContent = 'Rs. ' + totalAmount.toLocaleString();
      document.getElementById('summaryTotalProducts').textContent = totalProducts;
      document.getElementById('summaryTotalTransactions').textContent = totalProducts;
      document.getElementById('avgTransactionValue').textContent = 'Rs. ' + avgTransactionValue.toLocaleString();
      document.getElementById('totalQuantity').textContent = totalQuantity;
      document.getElementById('avgUnitPrice').textContent = 'Rs. ' + avgUnitPrice.toLocaleString();
      document.getElementById('grandTotal').textContent = 'Rs. ' + totalAmount.toLocaleString();
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', initializeReport);
  </script>

</body>
</html>
