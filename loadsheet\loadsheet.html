<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Inventory Report - ERP Style</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />

  <!-- Tom Select for searchable dropdowns -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet" />

  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    /* ===== Custom ERP-style Inputs ===== */
    @import url('https://rsms.me/inter/inter.css');

    input.form-control, select.form-select {
        border: none;
        border-bottom: 1px solid black;
        border-radius: 0;
        outline: none;
        box-shadow: none;
        font-size: 14px;
    }

    body {
        overflow-x: hidden;
        background-color: #f0f2f5;
        padding: 40px;
        font-family: 'Inter', sans-serif;
    }

    input.form-control:focus, select.form-select:focus {
        border: none;
        border-bottom: 1px solid black;
        outline: none;
        box-shadow: none;
    }

    label {
        font-size: 10px;
        width: 150px;
    }

    .form-label {
        font-size: 12px;
    }

    .form-control[readonly] {
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .table th, .table td {
        vertical-align: middle;
        text-align: center;
    }

    .card {
      padding: 25px;
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
    }

    hr {
      margin: 25px 0;
    }

    .form-check {
      margin-top: 8px;
    }

    /* Tom Select fix */
    .ts-wrapper.single .ts-control {
        border: none !important;
        border-bottom: 1px solid black !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        font-size: 14px;
    }
  </style>
</head>
<body>

  <div class="container-xl">
    <div class="card shadow-sm">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="section-title">Load Sheet</div>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="showHeading" checked>
          <label class="form-check-label" for="showHeading">Show Company Heading</label>
        </div>
      </div>
      <hr>

      <form>
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label">Select Product</label>
            <select class="form-select searchable" id="product">
              <option value="">Select Product</option>
              <option>Keyboard</option>
              <option>Mouse</option>
              <option>Monitor</option>
              <option>Printer</option>
              <option>Hard Drive</option>
            </select>
            <div class="form-check mt-2">
              <input class="form-check-input" type="checkbox" id="allProduct" checked>
              <label class="form-check-label" for="allProduct">All</label>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label">Select Warehouse</label>
            <select class="form-select searchable" id="warehouse">
              <option value="">Select Warehouse</option>
              <option>Warehouse A</option>
              <option>Warehouse B</option>
              <option>Warehouse C</option>
            </select>
            <div class="form-check mt-2">
              <input class="form-check-input" type="checkbox" id="allWarehouse" checked>
              <label class="form-check-label" for="allWarehouse">All</label>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label">Select Zone</label>
            <select class="form-select searchable" id="zone">
              <option value="">Select Zone</option>
              <option>East</option>
              <option>West</option>
              <option>North</option>
              <option>South</option>
            </select>
            <div class="form-check mt-2">
              <input class="form-check-input" type="checkbox" id="allZone" checked>
              <label class="form-check-label" for="allZone">All</label>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label">Select Company</label>
            <select class="form-select searchable" id="company">
              <option value="">Select Company</option>
              <option>Xenith Pvt Ltd</option>
              <option>Alpha Distributors</option>
              <option>Global Traders</option>
            </select>
            <div class="form-check mt-2">
              <input class="form-check-input" type="checkbox" id="allCompany" checked>
              <label class="form-check-label" for="allCompany">All</label>
            </div>
          </div>
        </div>

        <hr>

        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label">From Date</label>
            <input type="date" class="form-control" />
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label">To Date</label>
            <input type="date" class="form-control" />
          </div>
        </div>

        <div class="mt-3">
          <button type="submit" class="btn btn-primary">Generate Report</button>
        </div>
      </form>
    </div>
  </div>

  <!-- JS Libraries -->
  <script src="https://unpkg.com/@tabler/core@latest/dist/js/tabler.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script>
    // Enable searchable selects
    document.querySelectorAll('.searchable').forEach((el) => {
      new TomSelect(el, {
        create: false,
        sortField: {
          field: "text",
          direction: "asc"
        }
      });
    });
  </script>

</body>
</html>
