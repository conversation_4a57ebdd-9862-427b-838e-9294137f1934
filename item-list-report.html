<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Item List Report - ERP</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />
  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    @import url('https://rsms.me/inter/inter.css');

    body {
      font-family: 'Inter', sans-serif;
      padding: 40px;
      background: #fff;
      color: #000;
    }

    .report-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    /* Company Header */
    .company-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #000;
      padding-bottom: 20px;
    }

    .company-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
    }

    .company-name {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
      color: #000;
    }

    .company-address {
      font-size: 14px;
      color: #666;
      margin-bottom: 10px;
    }

    .report-title {
      font-size: 24px;
      font-weight: bold;
      color: #000;
      margin-top: 15px;
    }

    /* Report Info */
    .report-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .report-info-left, .report-info-right {
      flex: 1;
    }

    .info-item {
      margin-bottom: 8px;
      font-size: 14px;
    }

    .info-label {
      font-weight: bold;
      display: inline-block;
      width: 120px;
    }

    /* Table Styles */
    .report-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
      font-size: 12px;
    }

    .report-table th {
      background-color: #f8f9fa;
      border: 1px solid #000;
      padding: 10px 8px;
      text-align: center;
      font-weight: bold;
      font-size: 11px;
    }

    .report-table td {
      border: 1px solid #000;
      padding: 8px;
      text-align: center;
      font-size: 11px;
    }

    .report-table tbody tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    /* Summary Section */
    .report-summary {
      margin-top: 30px;
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .summary-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 14px;
    }

    /* Print Styles */
    @media print {
      body {
        padding: 20px;
        font-size: 12px;
      }
      
      .no-print {
        display: none !important;
      }
      
      .report-table {
        font-size: 10px;
      }
      
      .report-table th, .report-table td {
        padding: 6px 4px;
        font-size: 10px;
      }
      
      .company-name {
        font-size: 24px;
      }
      
      .report-title {
        font-size: 20px;
      }
    }

    /* Print Button */
    .print-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }

    .btn {
      margin-left: 10px;
    }
  </style>
</head>
<body>

  <!-- Print Controls -->
  <div class="print-controls no-print">
    <button class="btn btn-primary" onclick="window.print()">🖨️ Print Report</button>
    <button class="btn btn-secondary" onclick="window.close()">✕ Close</button>
  </div>

  <div class="report-container">
    <!-- Company Header -->
    <div class="company-header" id="companyHeader">
      <div class="company-name">Hzins International Pvt Ltd</div>
      <div class="company-address">
        123 Business District, Karachi, Pakistan<br>
        Phone: +92-21-1234567 | Email: <EMAIL>
      </div>
      <div class="report-title">Item List Report</div>
    </div>

    <!-- Report Information -->
    <div class="report-info">
      <div class="report-info-left">
        <div class="info-item">
          <span class="info-label">Branch:</span>
          <span id="reportBranch">-</span>
        </div>
        <div class="info-item">
          <span class="info-label">Price Type:</span>
          <span id="reportPriceType">-</span>
        </div>
      </div>
      <div class="report-info-right">
        <div class="info-item">
          <span class="info-label">Report Date:</span>
          <span id="reportDate">-</span>
        </div>
        <div class="info-item">
          <span class="info-label">Generated By:</span>
          <span>Admin User</span>
        </div>
      </div>
    </div>

    <!-- Items Table -->
    <table class="report-table">
      <thead>
        <tr>
          <th style="width: 8%;">S.No</th>
          <th style="width: 12%;">Item Code</th>
          <th style="width: 25%;">Item Name</th>
          <th style="width: 15%;">Category</th>
          <th style="width: 8%;">Unit</th>
          <th style="width: 15%;" id="priceColumnHeader">Price</th>
          <th style="width: 10%;">Stock Qty</th>
          <th style="width: 7%;">Status</th>
        </tr>
      </thead>
      <tbody id="reportTableBody">
        <!-- Data will be populated by JavaScript -->
      </tbody>
    </table>

    <!-- Report Summary -->
    <div class="report-summary">
      <div class="summary-title">Report Summary</div>
      <div class="summary-item">
        <span>Total Items:</span>
        <span id="totalItems">0</span>
      </div>
      <div class="summary-item">
        <span>Active Items:</span>
        <span id="activeItems">0</span>
      </div>
      <div class="summary-item">
        <span>Total Stock Value:</span>
        <span id="totalStockValue">Rs. 0</span>
      </div>
    </div>

    <!-- Footer -->
    <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
      <p>This is a computer generated report. No signature required.</p>
      <p>Generated on <span id="footerDate"></span> | Page 1 of 1</p>
    </div>
  </div>

  <script>
    // Sample item data (same as main page)
    const sampleItems = [
      { code: 'ITM001', name: 'Wireless Mouse', category: 'Electronics', unit: 'PCS', netRate: 1500, tradePrice: 1200, distributionPrice: 1000, stock: 50, status: 'Active' },
      { code: 'ITM002', name: 'Keyboard', category: 'Electronics', unit: 'PCS', netRate: 2500, tradePrice: 2000, distributionPrice: 1800, stock: 30, status: 'Active' },
      { code: 'ITM003', name: 'Monitor 24"', category: 'Electronics', unit: 'PCS', netRate: 25000, tradePrice: 22000, distributionPrice: 20000, stock: 15, status: 'Active' },
      { code: 'ITM004', name: 'USB Cable', category: 'Accessories', unit: 'PCS', netRate: 300, tradePrice: 250, distributionPrice: 200, stock: 100, status: 'Active' },
      { code: 'ITM005', name: 'Hard Drive 1TB', category: 'Storage', unit: 'PCS', netRate: 8000, tradePrice: 7200, distributionPrice: 6500, stock: 25, status: 'Active' },
      { code: 'ITM006', name: 'Printer Ink', category: 'Consumables', unit: 'PCS', netRate: 1200, tradePrice: 1000, distributionPrice: 900, stock: 75, status: 'Active' },
      { code: 'ITM007', name: 'Network Cable', category: 'Accessories', unit: 'MTR', netRate: 50, tradePrice: 40, distributionPrice: 35, stock: 500, status: 'Active' },
      { code: 'ITM008', name: 'Webcam HD', category: 'Electronics', unit: 'PCS', netRate: 3500, tradePrice: 3000, distributionPrice: 2800, stock: 20, status: 'Active' }
    ];

    // Get URL parameters
    function getUrlParams() {
      const params = new URLSearchParams(window.location.search);
      return {
        branch: params.get('branch') || '',
        priceType: params.get('priceType') || 'net_rate',
        showHeading: params.get('showHeading') === 'true',
        print: params.get('print') === 'true'
      };
    }

    // Initialize report
    function initializeReport() {
      const params = getUrlParams();
      
      // Set current date
      const currentDate = new Date().toLocaleDateString('en-GB');
      document.getElementById('reportDate').textContent = currentDate;
      document.getElementById('footerDate').textContent = currentDate;
      
      // Set branch name
      const branchNames = {
        'main': 'Main Branch',
        'karachi': 'Karachi Branch',
        'lahore': 'Lahore Branch',
        'islamabad': 'Islamabad Branch',
        'faisalabad': 'Faisalabad Branch',
        'multan': 'Multan Branch',
        'peshawar': 'Peshawar Branch',
        'quetta': 'Quetta Branch'
      };
      document.getElementById('reportBranch').textContent = branchNames[params.branch] || params.branch;
      
      // Set price type
      const priceTypeLabels = {
        'net_rate': 'Net Rate',
        'trade_price': 'Trade Price',
        'distribution_price': 'Distribution Price',
        'all': 'All Price Types'
      };
      document.getElementById('reportPriceType').textContent = priceTypeLabels[params.priceType];
      document.getElementById('priceColumnHeader').textContent = priceTypeLabels[params.priceType];
      
      // Show/hide company header
      if (!params.showHeading) {
        document.getElementById('companyHeader').style.display = 'none';
      }
      
      // Populate table
      populateTable(params.priceType);
      
      // Auto print if requested
      if (params.print) {
        setTimeout(() => {
          window.print();
        }, 1000);
      }
    }

    // Populate table with data
    function populateTable(priceType) {
      const tableBody = document.getElementById('reportTableBody');
      let totalStockValue = 0;
      let activeItems = 0;
      
      sampleItems.forEach((item, index) => {
        const row = document.createElement('tr');
        
        let priceDisplay = '';
        let itemPrice = 0;
        
        if (priceType === 'all') {
          priceDisplay = `N: ${item.netRate} | T: ${item.tradePrice} | D: ${item.distributionPrice}`;
          itemPrice = item.netRate; // Use net rate for calculation
        } else {
          const priceMap = {
            'net_rate': item.netRate,
            'trade_price': item.tradePrice,
            'distribution_price': item.distributionPrice
          };
          itemPrice = priceMap[priceType];
          priceDisplay = itemPrice.toLocaleString();
        }
        
        totalStockValue += (itemPrice * item.stock);
        if (item.status === 'Active') activeItems++;
        
        row.innerHTML = `
          <td>${index + 1}</td>
          <td>${item.code}</td>
          <td style="text-align: left;">${item.name}</td>
          <td>${item.category}</td>
          <td>${item.unit}</td>
          <td style="text-align: right;">${priceDisplay}</td>
          <td>${item.stock}</td>
          <td>${item.status}</td>
        `;
        
        tableBody.appendChild(row);
      });
      
      // Update summary
      document.getElementById('totalItems').textContent = sampleItems.length;
      document.getElementById('activeItems').textContent = activeItems;
      document.getElementById('totalStockValue').textContent = 'Rs. ' + totalStockValue.toLocaleString();
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', initializeReport);
  </script>

</body>
</html>
