<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Product Wise Sale Report - ERP Style</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />

  <!-- Tom Select for searchable dropdowns -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet" />

  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    /* ===== Custom ERP-style Inputs ===== */
    @import url('https://rsms.me/inter/inter.css');

    input.form-control, select.form-select {
        border: none;
        border-bottom: 1px solid black;
        border-radius: 0;
        outline: none;
        box-shadow: none;
        font-size: 14px;
    }

    body {
        overflow-x: hidden;
        background-color: #f0f2f5;
        padding: 40px;
        font-family: 'Inter', sans-serif;
    }

    input.form-control:focus, select.form-select:focus {
        border: none;
        border-bottom: 1px solid black;
        outline: none;
        box-shadow: none;
    }

    label {
        font-size: 10px;
        width: 150px;
    }

    .form-label {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .form-control[readonly] {
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .card {
      padding: 25px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.06);
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
    }

    hr {
      margin: 25px 0;
    }

    .form-check {
      margin-top: 8px;
      margin-bottom: 8px;
    }

    .form-check-input {
      margin-right: 8px;
    }

    .form-check-label {
      font-size: 14px;
      font-weight: 400;
    }

    /* Tom Select fix */
    .ts-wrapper.single .ts-control {
        border: none !important;
        border-bottom: 1px solid black !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        font-size: 14px;
    }

    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    .btn-primary:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }

    .btn-success {
      background-color: #28a745;
      border-color: #28a745;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    .btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    /* Sale Report Table */
    .sale-report-table {
      margin-top: 30px;
    }

    .table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
      background-color: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      font-weight: 600;
      color: #495057;
      font-size: 12px;
    }

    .table tbody td {
      font-size: 12px;
      vertical-align: middle;
    }

    /* Filter Section Styling */
    .filter-section {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 20px;
    }

    .filter-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #495057;
    }
  </style>
</head>
<body>

  <div class="container-xl">
    <div class="card shadow-sm">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="section-title">Product Wise Sale Report</div>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="showHeading" checked>
          <label class="form-check-label" for="showHeading">Show Company Heading</label>
        </div>
      </div>
      <hr>

      <form>
        <!-- Filter Section -->
        <div class="filter-section">
          <div class="filter-title">Report Filters</div>
          
          <!-- Row 1: Salesman, Town, Zone -->
          <div class="row mb-3">
            <div class="col-md-4">
              <label class="form-label">Select Salesman</label>
              <select class="form-select searchable" id="salesman">
                <option value="">Select Salesman</option>
                <option value="ahmed_ali">Ahmed Ali</option>
                <option value="muhammad_hassan">Muhammad Hassan</option>
                <option value="fatima_khan">Fatima Khan</option>
                <option value="ali_raza">Ali Raza</option>
                <option value="sara_ahmed">Sara Ahmed</option>
                <option value="usman_malik">Usman Malik</option>
                <option value="ayesha_shah">Ayesha Shah</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allSalesman">
                <label class="form-check-label" for="allSalesman">All</label>
              </div>
            </div>
            <div class="col-md-4">
              <label class="form-label">Select Town</label>
              <select class="form-select searchable" id="town">
                <option value="">Select Town</option>
                <option value="karachi">Karachi</option>
                <option value="lahore">Lahore</option>
                <option value="islamabad">Islamabad</option>
                <option value="faisalabad">Faisalabad</option>
                <option value="rawalpindi">Rawalpindi</option>
                <option value="multan">Multan</option>
                <option value="peshawar">Peshawar</option>
                <option value="quetta">Quetta</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allTown">
                <label class="form-check-label" for="allTown">All</label>
              </div>
            </div>
            <div class="col-md-4">
              <label class="form-label">Select Zone</label>
              <select class="form-select searchable" id="zone">
                <option value="">Select Zone</option>
                <option value="north">North Zone</option>
                <option value="south">South Zone</option>
                <option value="east">East Zone</option>
                <option value="west">West Zone</option>
                <option value="central">Central Zone</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allZone">
                <label class="form-check-label" for="allZone">All</label>
              </div>
            </div>
          </div>

          <!-- Row 2: Customer, Company, Warehouse -->
          <div class="row mb-3">
            <div class="col-md-4">
              <label class="form-label">Select Customer</label>
              <select class="form-select searchable" id="customer">
                <option value="">Select Customer</option>
                <option value="abc_traders">ABC Traders</option>
                <option value="xyz_enterprises">XYZ Enterprises</option>
                <option value="global_solutions">Global Solutions</option>
                <option value="tech_world">Tech World</option>
                <option value="mega_store">Mega Store</option>
                <option value="prime_electronics">Prime Electronics</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allCustomer">
                <label class="form-check-label" for="allCustomer">All</label>
              </div>
            </div>
            <div class="col-md-4">
              <label class="form-label">Select Company</label>
              <select class="form-select searchable" id="company">
                <option value="">Select Company</option>
                <option value="hzins_intl">Hzins International Pvt Ltd</option>
                <option value="alpha_dist">Alpha Distributors</option>
                <option value="beta_corp">Beta Corporation</option>
                <option value="gamma_ltd">Gamma Limited</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allCompany">
                <label class="form-check-label" for="allCompany">All</label>
              </div>
            </div>
            <div class="col-md-4">
              <label class="form-label">Select Warehouse</label>
              <select class="form-select searchable" id="warehouse">
                <option value="">Select Warehouse</option>
                <option value="main_warehouse">Main Warehouse</option>
                <option value="karachi_wh">Karachi Warehouse</option>
                <option value="lahore_wh">Lahore Warehouse</option>
                <option value="islamabad_wh">Islamabad Warehouse</option>
                <option value="faisalabad_wh">Faisalabad Warehouse</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allWarehouse">
                <label class="form-check-label" for="allWarehouse">All</label>
              </div>
            </div>
          </div>

          <!-- Row 3: Date Range -->
          <div class="row mb-3">
            <div class="col-md-6">
              <label class="form-label">From Date</label>
              <input type="date" class="form-control" id="fromDate" required>
            </div>
            <div class="col-md-6">
              <label class="form-label">To Date</label>
              <input type="date" class="form-control" id="toDate" required>
            </div>
          </div>
        </div>

        <!-- Generate Report Buttons -->
        <div class="row mb-4">
          <div class="col-12">
            <button type="button" class="btn btn-primary me-3" onclick="generateSaleReport()">
              Generate Sale Report
            </button>
            <button type="button" class="btn btn-success" onclick="generateReport()" id="reportBtn" style="display: none;">
              📊 Generate Report
            </button>
            <button type="button" class="btn btn-secondary ms-2" onclick="printReport()" id="printBtn" style="display: none;">
              🖨️ Print Report
            </button>
          </div>
        </div>
      </form>

      <!-- Sale Report Table -->
      <div class="sale-report-table" id="saleReportTable" style="display: none;">
        <h4 class="mb-3">Product Wise Sale Report Results</h4>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>S.No</th>
                <th>Product Code</th>
                <th>Product Name</th>
                <th>Customer</th>
                <th>Salesman</th>
                <th>Qty Sold</th>
                <th>Unit Price</th>
                <th>Total Amount</th>
                <th>Sale Date</th>
              </tr>
            </thead>
            <tbody id="saleTableBody">
              <!-- Sample data will be populated here -->
            </tbody>
          </table>
        </div>
        
        <!-- Summary Section -->
        <div class="row mt-4">
          <div class="col-md-6">
            <div class="card bg-light">
              <div class="card-body">
                <h6 class="card-title">Report Summary</h6>
                <p class="mb-1"><strong>Total Products:</strong> <span id="totalProducts">0</span></p>
                <p class="mb-1"><strong>Total Quantity:</strong> <span id="totalQuantity">0</span></p>
                <p class="mb-0"><strong>Total Sales Amount:</strong> <span id="totalAmount">Rs. 0</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- JS Libraries -->
  <script src="https://unpkg.com/@tabler/core@latest/dist/js/tabler.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script>
    // Enable searchable selects
    document.querySelectorAll('.searchable').forEach((el) => {
      new TomSelect(el, {
        create: false,
        sortField: {
          field: "text",
          direction: "asc"
        }
      });
    });

    // Set default dates (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('toDate').value = today.toISOString().split('T')[0];
    document.getElementById('fromDate').value = thirtyDaysAgo.toISOString().split('T')[0];

    // Sample sale data
    const sampleSaleData = [
      { productCode: 'ITM001', productName: 'Wireless Mouse', customer: 'ABC Traders', customerId: 'abc_traders', salesman: 'Ahmed Ali', salesmanId: 'ahmed_ali', qty: 10, unitPrice: 1500, saleDate: '2024-01-15' },
      { productCode: 'ITM002', productName: 'Keyboard', customer: 'XYZ Enterprises', customerId: 'xyz_enterprises', salesman: 'Muhammad Hassan', salesmanId: 'muhammad_hassan', qty: 5, unitPrice: 2500, saleDate: '2024-01-16' },
      { productCode: 'ITM003', productName: 'Monitor 24"', customer: 'Global Solutions', customerId: 'global_solutions', salesman: 'Fatima Khan', salesmanId: 'fatima_khan', qty: 2, unitPrice: 25000, saleDate: '2024-01-17' },
      { productCode: 'ITM001', productName: 'Wireless Mouse', customer: 'Tech World', customerId: 'tech_world', salesman: 'Ali Raza', salesmanId: 'ali_raza', qty: 15, unitPrice: 1500, saleDate: '2024-01-18' },
      { productCode: 'ITM004', productName: 'USB Cable', customer: 'Mega Store', customerId: 'mega_store', salesman: 'Sara Ahmed', salesmanId: 'sara_ahmed', qty: 50, unitPrice: 300, saleDate: '2024-01-19' },
      { productCode: 'ITM005', productName: 'Hard Drive 1TB', customer: 'Prime Electronics', customerId: 'prime_electronics', salesman: 'Usman Malik', salesmanId: 'usman_malik', qty: 8, unitPrice: 8000, saleDate: '2024-01-20' },
      { productCode: 'ITM002', productName: 'Keyboard', customer: 'ABC Traders', customerId: 'abc_traders', salesman: 'Ayesha Shah', salesmanId: 'ayesha_shah', qty: 12, unitPrice: 2500, saleDate: '2024-01-21' },
      { productCode: 'ITM003', productName: 'Monitor 24"', customer: 'XYZ Enterprises', customerId: 'xyz_enterprises', salesman: 'Ahmed Ali', salesmanId: 'ahmed_ali', qty: 3, unitPrice: 25000, saleDate: '2024-01-22' },
      { productCode: 'ITM006', productName: 'Printer Ink', customer: 'Tech World', customerId: 'tech_world', salesman: 'Ahmed Ali', salesmanId: 'ahmed_ali', qty: 20, unitPrice: 1200, saleDate: '2024-01-23' },
      { productCode: 'ITM007', productName: 'Network Cable', customer: 'Global Solutions', customerId: 'global_solutions', salesman: 'Muhammad Hassan', salesmanId: 'muhammad_hassan', qty: 100, unitPrice: 50, saleDate: '2024-01-24' }
    ];

    // Generate sale report function
    function generateSaleReport() {
      const fromDate = document.getElementById('fromDate').value;
      const toDate = document.getElementById('toDate').value;

      if (!fromDate || !toDate) {
        alert('Please select both From Date and To Date');
        return;
      }

      if (new Date(fromDate) > new Date(toDate)) {
        alert('From Date cannot be greater than To Date');
        return;
      }

      // Get filter values
      const selectedSalesman = document.getElementById('salesman').value;
      const selectedTown = document.getElementById('town').value;
      const selectedZone = document.getElementById('zone').value;
      const selectedCustomer = document.getElementById('customer').value;
      const selectedCompany = document.getElementById('company').value;
      const selectedWarehouse = document.getElementById('warehouse').value;

      // Check "All" checkboxes
      const allSalesman = document.getElementById('allSalesman').checked;
      const allTown = document.getElementById('allTown').checked;
      const allZone = document.getElementById('allZone').checked;
      const allCustomer = document.getElementById('allCustomer').checked;
      const allCompany = document.getElementById('allCompany').checked;
      const allWarehouse = document.getElementById('allWarehouse').checked;

      // Filter data based on selections
      let filteredData = sampleSaleData.filter(sale => {
        // Filter by salesman
        if (!allSalesman && selectedSalesman && sale.salesmanId !== selectedSalesman) {
          return false;
        }

        // Filter by customer
        if (!allCustomer && selectedCustomer && sale.customerId !== selectedCustomer) {
          return false;
        }

        // Filter by date range
        const saleDate = new Date(sale.saleDate);
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);

        if (saleDate < fromDateObj || saleDate > toDateObj) {
          return false;
        }

        return true;
      });

      // Populate table
      const tableBody = document.getElementById('saleTableBody');
      tableBody.innerHTML = '';

      let totalProducts = 0;
      let totalQuantity = 0;
      let totalAmount = 0;

      if (filteredData.length === 0) {
        // Show no data message
        const noDataRow = document.createElement('tr');
        noDataRow.innerHTML = `
          <td colspan="9" style="text-align: center; padding: 20px; color: #666;">
            No sales data found for the selected filters
          </td>
        `;
        tableBody.appendChild(noDataRow);
      } else {
        filteredData.forEach((sale, index) => {
          const row = document.createElement('tr');
          const amount = sale.qty * sale.unitPrice;

          totalProducts++;
          totalQuantity += sale.qty;
          totalAmount += amount;

          row.innerHTML = `
            <td>${index + 1}</td>
            <td>${sale.productCode}</td>
            <td>${sale.productName}</td>
            <td>${sale.customer}</td>
            <td>${sale.salesman}</td>
            <td>${sale.qty}</td>
            <td>Rs. ${sale.unitPrice.toLocaleString()}</td>
            <td>Rs. ${amount.toLocaleString()}</td>
            <td>${new Date(sale.saleDate).toLocaleDateString('en-GB')}</td>
          `;

          tableBody.appendChild(row);
        });
      }

      // Update summary
      document.getElementById('totalProducts').textContent = totalProducts;
      document.getElementById('totalQuantity').textContent = totalQuantity;
      document.getElementById('totalAmount').textContent = 'Rs. ' + totalAmount.toLocaleString();

      // Show the table and buttons
      document.getElementById('saleReportTable').style.display = 'block';
      document.getElementById('reportBtn').style.display = 'inline-block';
      document.getElementById('printBtn').style.display = 'inline-block';
    }

    // Generate Report function
    function generateReport() {
      const params = getReportParams();
      
      // Create URL parameters
      const urlParams = new URLSearchParams(params);
      
      // Open report in new window
      window.open(`product-wise-sale-report-print.html?${urlParams.toString()}`, '_blank');
    }

    // Print Report function
    function printReport() {
      const params = getReportParams();
      params.print = true;
      
      // Create URL parameters
      const urlParams = new URLSearchParams(params);
      
      // Open print version in new window
      const printWindow = window.open(`product-wise-sale-report-print.html?${urlParams.toString()}`, '_blank');
      
      // Auto print when loaded
      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
        }, 500);
      };
    }

    // Get report parameters
    function getReportParams() {
      return {
        salesman: document.getElementById('salesman').value,
        town: document.getElementById('town').value,
        zone: document.getElementById('zone').value,
        customer: document.getElementById('customer').value,
        company: document.getElementById('company').value,
        warehouse: document.getElementById('warehouse').value,
        fromDate: document.getElementById('fromDate').value,
        toDate: document.getElementById('toDate').value,
        showHeading: document.getElementById('showHeading').checked
      };
    }
  </script>

</body>
</html>
