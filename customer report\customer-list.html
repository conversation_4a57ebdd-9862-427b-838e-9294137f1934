<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Customer List</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Tabler CSS -->
  <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet" />
  <!-- Tom Select CSS -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.bootstrap5.min.css" rel="stylesheet" />

<style>
  body {
    background-color: #f5f7fb;
    padding: 40px;
  }

  .form-card {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.06);
    position: relative;
    z-index: 1;
  }

  .form-label {
    font-weight: 500;
    margin-bottom: 5px;
  }

  .btn-primary {
    border-radius: 10px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .ts-wrapper {
    z-index: 1000 !important;
  }

  .ts-dropdown {
    z-index: 2000 !important;
  }

  .ts-dropdown-content {
    max-height: 200px;         /* 👈 Scroll height limit */
    overflow-y: auto;          /* 👈 Enables vertical scrolling */
    background-color: white;
  }
</style>
</head>
<body>

  <div class="container-xl">
    <div class="form-card">
      <h2 class="mb-4">Customer List</h2>

      <div class="row mb-3">
        <div class="col-md-6 mb-3">
          <label for="salesman" class="form-label">Select Salesman:</label>
          <select id="salesman" class="form-select">
            <option value="sameer">sameer</option>
            <option value="hadiya">hadiya</option>
            <option value="arsalan">arsalan</option>
            <option value="junaid">junaid</option>
             <option value="c">c</option>
              <option value="a">a</option>
               <option value="s">s</option>
                <option value="d">d</option>
                 <option value="f">f</option>
                  <option value="g">g</option>
                   <option value="h">h</option>
                    <option value="x">x</option>

                     <option value="v">v</option>
          </select>
        </div>

        <div class="col-md-6 mb-3">
          <label for="town" class="form-label">Select Town:</label>
          <select id="town" class="form-select">
            <option value="town 1">town 1</option>
            <option value="town 2">town 2</option>
            <option value="town 3">town 3</option>
            <option value="town 4">town 4</option>
          </select>
        </div>
      </div>

      <div class="row mb-4">
        <div class="col-md-6">
          <label for="category" class="form-label">Select Customer Category:</label>
          <select id="category" class="form-select">
            <option disabled selected>Select Customer Category</option>
            <option value="retail">Retail</option>
            <option value="wholesale">Wholesale</option>
            <option value="distributor">Distributor</option>
          </select>
        </div>
      </div>

      <button class="btn btn-primary">Generate Report</button>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>

  <script>
    document.addEventListener("DOMContentLoaded", function () {
      const options = {
        create: false,
        dropdownParent: 'body', // make sure it shows below
        render: {
          option_create: function(data, escape) {
            return '<div class="create">Add <strong>' + escape(data.input) + '</strong>&hellip;</div>';
          }
        }
      };

      new TomSelect("#salesman", options);
      new TomSelect("#town", options);
      new TomSelect("#category", options);
    });
  </script>
</body>
</html>
