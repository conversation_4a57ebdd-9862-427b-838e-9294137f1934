
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stock Ledger Report</title>
  <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet">
  <style>
    .card {
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #fff;
      border-bottom: 1px solid #e9ecef;
      padding: 1rem;
    }
    .card-title {
      color: #2c3e50;
      font-size: 1.25rem;
      font-weight: 600;
    }
    .form-group {
      margin-bottom: 1.5rem;
    }
    .form-control {
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
      height: calc(1.5em + 0.75rem + 2px);
    }
    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      padding: 0.5rem 1.5rem;
      font-weight: 500;
      border-radius: 0.25rem;
    }
    .btn-primary:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }
    @media (max-width: 768px) {
      .row {
        flex-direction: column;
      }
      .col-md-4 {
        width: 100%;
        margin-bottom: 1rem;
      }
    }
  </style>
</head>
<body>
  <div class="container mt-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Stock Ledger Report</h3>
      </div>
      <div class="card-body p-4">
        <div class="form-group row">
          <div class="col-md-4">
            <label>Select Product:</label>
            <select class="form-control">
              <option>Select Product</option>
            </select>
          </div>
          <div class="col-md-4">
            <label>From Date:</label>
            <input type="date" class="form-control" placeholder="mm/dd/yyyy">
          </div>
          <div class="col-md-4">
            <label>To Date:</label>
            <input type="date" class="form-control" placeholder="mm/dd/yyyy">
          </div>
        </div>
        <button class="btn btn-primary" onclick="window.print()">Generate Report</button>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
</body>
</html>