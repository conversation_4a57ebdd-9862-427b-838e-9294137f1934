<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Last Purchase/Sale Report - ERP Style</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />

  <!-- Tom Select for searchable dropdowns -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet" />

  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    /* ===== Custom ERP-style Inputs ===== */
    @import url('https://rsms.me/inter/inter.css');

    input.form-control, select.form-select {
        border: none;
        border-bottom: 1px solid black;
        border-radius: 0;
        outline: none;
        box-shadow: none;
        font-size: 14px;
    }

    body {
        overflow-x: hidden;
        background-color: #f0f2f5;
        padding: 40px;
        font-family: 'Inter', sans-serif;
    }

    input.form-control:focus, select.form-select:focus {
        border: none;
        border-bottom: 1px solid black;
        outline: none;
        box-shadow: none;
    }

    label {
        font-size: 10px;
        width: 150px;
    }

    .form-label {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .form-control[readonly] {
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .card {
      padding: 25px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.06);
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
    }

    hr {
      margin: 25px 0;
    }

    .form-check {
      margin-top: 8px;
      margin-bottom: 8px;
    }

    .form-check-input {
      margin-right: 8px;
    }

    .form-check-label {
      font-size: 14px;
      font-weight: 400;
    }

    /* Transaction Type Section Styling */
    .transaction-type-section {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .transaction-type-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #495057;
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
    }

    .radio-item {
      display: flex;
      align-items: center;
    }

    .radio-item input[type="radio"] {
      margin-right: 8px;
      transform: scale(1.2);
    }

    .radio-item label {
      font-size: 16px;
      font-weight: 500;
      width: auto;
      margin: 0;
      cursor: pointer;
    }

    /* Tom Select fix */
    .ts-wrapper.single .ts-control {
        border: none !important;
        border-bottom: 1px solid black !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        font-size: 14px;
    }

    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    .btn-success {
      background-color: #28a745;
      border-color: #28a745;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    .btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    /* Report Table */
    .report-table-section {
      margin-top: 30px;
    }

    .table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
      background-color: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      font-weight: 600;
      color: #495057;
      font-size: 12px;
    }

    .table tbody td {
      font-size: 12px;
      vertical-align: middle;
    }

    /* Filter Section Styling */
    .filter-section {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      margin-bottom: 20px;
    }

    .filter-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #495057;
    }

    .badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
    }

    .bg-success {
      background-color: #28a745 !important;
    }

    .bg-info {
      background-color: #17a2b8 !important;
    }

    .text-white {
      color: white !important;
    }
  </style>
</head>
<body>

  <div class="container-xl">
    <div class="card shadow-sm">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="section-title">Last Purchase/Sale Report</div>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="showHeading" checked>
          <label class="form-check-label" for="showHeading">Show Company Heading</label>
        </div>
      </div>
      <hr>

      <form>
        <!-- Filter Section -->
        <div class="filter-section">
          <div class="filter-title">Report Filters</div>
          
          <!-- Product Selection -->
          <div class="row mb-4">
            <div class="col-md-6">
              <label class="form-label">Select Product</label>
              <select class="form-select searchable" id="product">
                <option value="">Select Product</option>
                <option value="ITM001">ITM001 - Wireless Mouse</option>
                <option value="ITM002">ITM002 - Keyboard</option>
                <option value="ITM003">ITM003 - Monitor 24"</option>
                <option value="ITM004">ITM004 - USB Cable</option>
                <option value="ITM005">ITM005 - Hard Drive 1TB</option>
                <option value="ITM006">ITM006 - Printer Ink</option>
                <option value="ITM007">ITM007 - Network Cable</option>
                <option value="ITM008">ITM008 - Webcam HD</option>
              </select>
              <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" id="allProducts">
                <label class="form-check-label" for="allProducts">All Products</label>
              </div>
            </div>
          </div>

          <!-- Transaction Type Selection -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="transaction-type-section">
                <div class="transaction-type-title">Select Transaction Type</div>
                <div class="radio-group">
                  <div class="radio-item">
                    <input type="radio" id="lastSale" name="transactionType" value="last_sale" checked>
                    <label for="lastSale">Last Sale</label>
                  </div>
                  <div class="radio-item">
                    <input type="radio" id="lastPurchase" name="transactionType" value="last_purchase">
                    <label for="lastPurchase">Last Purchase</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Generate Report Buttons -->
        <div class="row mb-4">
          <div class="col-12">
            <button type="button" class="btn btn-primary me-3" onclick="generateLastTransactionReport()">
              Generate Report
            </button>
            <button type="button" class="btn btn-success" onclick="generateDetailedReport()" id="detailedReportBtn" style="display: none;">
              📊 Detailed Report
            </button>
            <button type="button" class="btn btn-secondary ms-2" onclick="printReport()" id="printBtn" style="display: none;">
              🖨️ Print Report
            </button>
          </div>
        </div>
      </form>

      <!-- Report Table -->
      <div class="report-table-section" id="reportTableSection" style="display: none;">
        <h4 class="mb-3" id="reportTitle">Last Transaction Report Results</h4>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr id="tableHeader">
                <!-- Headers will be populated dynamically -->
              </tr>
            </thead>
            <tbody id="reportTableBody">
              <!-- Data will be populated here -->
            </tbody>
          </table>
        </div>
        
        <!-- Summary Section -->
        <div class="row mt-4">
          <div class="col-md-8">
            <div class="card bg-light">
              <div class="card-body">
                <h6 class="card-title">Report Summary</h6>
                <p class="mb-1"><strong>Total Products:</strong> <span id="totalProducts">0</span></p>
                <p class="mb-1"><strong>Total Transactions:</strong> <span id="totalTransactions">0</span></p>
                <p class="mb-0"><strong>Total Value:</strong> <span id="totalValue">Rs. 0</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- JS Libraries -->
  <script src="https://unpkg.com/@tabler/core@latest/dist/js/tabler.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script>
    // Enable searchable selects
    document.querySelectorAll('.searchable').forEach((el) => {
      new TomSelect(el, {
        create: false,
        sortField: {
          field: "text",
          direction: "asc"
        }
      });
    });

    // Sample transaction data
    const sampleTransactionData = {
      lastSale: [
        { productCode: 'ITM001', productName: 'Wireless Mouse', customer: 'ABC Traders', salesman: 'Ahmed Ali', qty: 10, unitPrice: 1500, transactionDate: '2024-01-22', invoiceNo: 'INV-2024-001' },
        { productCode: 'ITM002', productName: 'Keyboard', customer: 'XYZ Enterprises', salesman: 'Muhammad Hassan', qty: 12, unitPrice: 2500, transactionDate: '2024-01-21', invoiceNo: 'INV-2024-002' },
        { productCode: 'ITM003', productName: 'Monitor 24"', customer: 'Global Solutions', salesman: 'Fatima Khan', qty: 3, unitPrice: 25000, transactionDate: '2024-01-20', invoiceNo: 'INV-2024-003' },
        { productCode: 'ITM004', productName: 'USB Cable', customer: 'Tech World', salesman: 'Ali Raza', qty: 50, unitPrice: 300, transactionDate: '2024-01-19', invoiceNo: 'INV-2024-004' },
        { productCode: 'ITM005', productName: 'Hard Drive 1TB', customer: 'Prime Electronics', salesman: 'Sara Ahmed', qty: 8, unitPrice: 8000, transactionDate: '2024-01-18', invoiceNo: 'INV-2024-005' },
        { productCode: 'ITM006', productName: 'Printer Ink', customer: 'Mega Store', salesman: 'Usman Malik', qty: 20, unitPrice: 1200, transactionDate: '2024-01-17', invoiceNo: 'INV-2024-006' },
        { productCode: 'ITM007', productName: 'Network Cable', customer: 'Tech Solutions', salesman: 'Ayesha Shah', qty: 100, unitPrice: 50, transactionDate: '2024-01-16', invoiceNo: 'INV-2024-007' },
        { productCode: 'ITM008', productName: 'Webcam HD', customer: 'Digital World', salesman: 'Hassan Ali', qty: 5, unitPrice: 3500, transactionDate: '2024-01-15', invoiceNo: 'INV-2024-008' }
      ],
      lastPurchase: [
        { productCode: 'ITM001', productName: 'Wireless Mouse', supplier: 'Tech Suppliers Ltd', purchaseOfficer: 'Usman Khan', qty: 100, unitPrice: 1200, transactionDate: '2024-01-15', poNumber: 'PO-2024-001' },
        { productCode: 'ITM002', productName: 'Keyboard', supplier: 'Global Tech Inc', purchaseOfficer: 'Ayesha Shah', qty: 50, unitPrice: 2000, transactionDate: '2024-01-14', poNumber: 'PO-2024-002' },
        { productCode: 'ITM003', productName: 'Monitor 24"', supplier: 'Display Solutions', purchaseOfficer: 'Hassan Ali', qty: 20, unitPrice: 22000, transactionDate: '2024-01-13', poNumber: 'PO-2024-003' },
        { productCode: 'ITM004', productName: 'USB Cable', supplier: 'Cable World', purchaseOfficer: 'Fatima Ahmed', qty: 200, unitPrice: 250, transactionDate: '2024-01-12', poNumber: 'PO-2024-004' },
        { productCode: 'ITM005', productName: 'Hard Drive 1TB', supplier: 'Storage Plus', purchaseOfficer: 'Ali Hassan', qty: 30, unitPrice: 7200, transactionDate: '2024-01-11', poNumber: 'PO-2024-005' },
        { productCode: 'ITM006', productName: 'Printer Ink', supplier: 'Ink Solutions', purchaseOfficer: 'Sara Ahmed', qty: 80, unitPrice: 1000, transactionDate: '2024-01-10', poNumber: 'PO-2024-006' },
        { productCode: 'ITM007', productName: 'Network Cable', supplier: 'Network Plus', purchaseOfficer: 'Ahmed Ali', qty: 500, unitPrice: 40, transactionDate: '2024-01-09', poNumber: 'PO-2024-007' },
        { productCode: 'ITM008', productName: 'Webcam HD', supplier: 'Camera World', purchaseOfficer: 'Fatima Khan', qty: 25, unitPrice: 3000, transactionDate: '2024-01-08', poNumber: 'PO-2024-008' }
      ]
    };

    // Generate last transaction report function
    function generateLastTransactionReport() {
      // Get form values
      const selectedProduct = document.getElementById('product').value;
      const allProducts = document.getElementById('allProducts').checked;
      const transactionType = document.querySelector('input[name="transactionType"]:checked').value;

      if (!allProducts && !selectedProduct) {
        alert('Please select a product or check "All Products"');
        return;
      }

      // Get data based on transaction type
      let data = sampleTransactionData[transactionType];
      
      // Filter by product if specific product is selected
      if (!allProducts && selectedProduct) {
        data = data.filter(item => item.productCode === selectedProduct);
      }

      // Update table headers based on transaction type
      const tableHeader = document.getElementById('tableHeader');
      const reportTitle = document.getElementById('reportTitle');
      
      if (transactionType === 'last_sale') {
        reportTitle.textContent = 'Last Sale Report Results';
        tableHeader.innerHTML = `
          <th>S.No</th>
          <th>Product Code</th>
          <th>Product Name</th>
          <th>Customer</th>
          <th>Salesman</th>
          <th>Qty Sold</th>
          <th>Unit Price</th>
          <th>Total Amount</th>
          <th>Sale Date</th>
          <th>Invoice No</th>
        `;
      } else {
        reportTitle.textContent = 'Last Purchase Report Results';
        tableHeader.innerHTML = `
          <th>S.No</th>
          <th>Product Code</th>
          <th>Product Name</th>
          <th>Supplier</th>
          <th>Purchase Officer</th>
          <th>Qty Purchased</th>
          <th>Unit Price</th>
          <th>Total Amount</th>
          <th>Purchase Date</th>
          <th>PO Number</th>
        `;
      }

      // Populate table
      const tableBody = document.getElementById('reportTableBody');
      tableBody.innerHTML = '';

      let totalProducts = 0;
      let totalTransactions = 0;
      let totalValue = 0;

      if (data.length === 0) {
        const noDataRow = document.createElement('tr');
        noDataRow.innerHTML = `
          <td colspan="10" style="text-align: center; padding: 20px; color: #666;">
            No ${transactionType === 'last_sale' ? 'sale' : 'purchase'} data found for the selected product(s)
          </td>
        `;
        tableBody.appendChild(noDataRow);
      } else {
        data.forEach((transaction, index) => {
          const row = document.createElement('tr');
          const amount = transaction.qty * transaction.unitPrice;
          
          totalProducts++;
          totalTransactions++;
          totalValue += amount;

          if (transactionType === 'last_sale') {
            row.innerHTML = `
              <td>${index + 1}</td>
              <td>${transaction.productCode}</td>
              <td>${transaction.productName}</td>
              <td>${transaction.customer}</td>
              <td>${transaction.salesman}</td>
              <td>${transaction.qty}</td>
              <td>Rs. ${transaction.unitPrice.toLocaleString()}</td>
              <td>Rs. ${amount.toLocaleString()}</td>
              <td>${new Date(transaction.transactionDate).toLocaleDateString('en-GB')}</td>
              <td><span class="badge bg-success text-white">${transaction.invoiceNo}</span></td>
            `;
          } else {
            row.innerHTML = `
              <td>${index + 1}</td>
              <td>${transaction.productCode}</td>
              <td>${transaction.productName}</td>
              <td>${transaction.supplier}</td>
              <td>${transaction.purchaseOfficer}</td>
              <td>${transaction.qty}</td>
              <td>Rs. ${transaction.unitPrice.toLocaleString()}</td>
              <td>Rs. ${amount.toLocaleString()}</td>
              <td>${new Date(transaction.transactionDate).toLocaleDateString('en-GB')}</td>
              <td><span class="badge bg-info text-white">${transaction.poNumber}</span></td>
            `;
          }
          
          tableBody.appendChild(row);
        });
      }

      // Update summary
      document.getElementById('totalProducts').textContent = totalProducts;
      document.getElementById('totalTransactions').textContent = totalTransactions;
      document.getElementById('totalValue').textContent = 'Rs. ' + totalValue.toLocaleString();

      // Show the table and buttons
      document.getElementById('reportTableSection').style.display = 'block';
      document.getElementById('detailedReportBtn').style.display = 'inline-block';
      document.getElementById('printBtn').style.display = 'inline-block';

      console.log('Report generated successfully');
    }

    // Test function to check if JavaScript is working
    function testFunction() {
      alert('JavaScript is working! Function called successfully.');
      console.log('Test function called');
    }

    // Generate detailed report function
    function generateDetailedReport() {
      const params = getReportParams();
      
      // Create URL parameters
      const urlParams = new URLSearchParams(params);
      
      // Open report in new window
      window.open(`last-purchase-sale-report-print.html?${urlParams.toString()}`, '_blank');
    }

    // Print Report function
    function printReport() {
      const params = getReportParams();
      params.print = true;
      
      // Create URL parameters
      const urlParams = new URLSearchParams(params);
      
      // Open print version in new window
      const printWindow = window.open(`last-purchase-sale-report-print.html?${urlParams.toString()}`, '_blank');
      
      // Auto print when loaded
      printWindow.onload = function() {
        setTimeout(() => {
          printWindow.print();
        }, 500);
      };
    }

    // Get report parameters
    function getReportParams() {
      const allProducts = document.getElementById('allProducts').checked;
      return {
        product: allProducts ? '' : document.getElementById('product').value,
        allProducts: allProducts,
        transactionType: document.querySelector('input[name="transactionType"]:checked').value,
        showHeading: document.getElementById('showHeading').checked
      };
    }

    // Handle "All Products" checkbox change
    document.getElementById('allProducts').addEventListener('change', function() {
      const productSelect = document.getElementById('product');
      
      if (this.checked) {
        productSelect.disabled = true;
        productSelect.style.backgroundColor = '#e9ecef';
      } else {
        productSelect.disabled = false;
        productSelect.style.backgroundColor = '';
      }
    });
  </script>

</body>
</html>
