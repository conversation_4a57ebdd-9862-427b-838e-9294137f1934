<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Test Report - Simple Version</title>
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />
</head>
<body>
  <div class="container mt-5">
    <div class="card">
      <div class="card-body">
        <h3>Test Report Generation</h3>
        
        <div class="mb-3">
          <label class="form-label">Select Product</label>
          <select class="form-select" id="product">
            <option value="">Select Product</option>
            <option value="ITM001">ITM001 - Wireless Mouse</option>
            <option value="ITM002">ITM002 - Keyboard</option>
          </select>
        </div>
        
        <div class="mb-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="allProducts">
            <label class="form-check-label" for="allProducts">All Products</label>
          </div>
        </div>
        
        <div class="mb-3">
          <label class="form-label">Transaction Type</label>
          <div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="transactionType" value="last_sale" id="lastSale" checked>
              <label class="form-check-label" for="lastSale">Last Sale</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="transactionType" value="last_purchase" id="lastPurchase">
              <label class="form-check-label" for="lastPurchase">Last Purchase</label>
            </div>
          </div>
        </div>
        
        <button type="button" class="btn btn-primary" onclick="generateReport()">
          Generate Report
        </button>
        
        <div id="reportResult" class="mt-4" style="display: none;">
          <h4>Report Generated Successfully!</h4>
          <div id="reportData"></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    function generateReport() {
      console.log('Generate report function called');
      alert('Function is working!');
      
      // Get values
      const product = document.getElementById('product').value;
      const allProducts = document.getElementById('allProducts').checked;
      const transactionType = document.querySelector('input[name="transactionType"]:checked').value;
      
      console.log('Product:', product);
      console.log('All Products:', allProducts);
      console.log('Transaction Type:', transactionType);
      
      // Show result
      const resultDiv = document.getElementById('reportResult');
      const dataDiv = document.getElementById('reportData');
      
      dataDiv.innerHTML = `
        <p><strong>Product:</strong> ${allProducts ? 'All Products' : (product || 'None selected')}</p>
        <p><strong>Transaction Type:</strong> ${transactionType}</p>
        <p><strong>Status:</strong> Report generated successfully!</p>
      `;
      
      resultDiv.style.display = 'block';
    }
  </script>
</body>
</html>
