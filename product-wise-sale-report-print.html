<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Product Wise Sale Report - Print</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />
  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    @import url('https://rsms.me/inter/inter.css');

    body {
      font-family: 'Inter', sans-serif;
      padding: 40px;
      background: #fff;
      color: #000;
    }

    .report-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    /* Company Header */
    .company-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #000;
      padding-bottom: 20px;
    }

    .company-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
    }

    .company-name {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
      color: #000;
    }

    .company-address {
      font-size: 14px;
      color: #666;
      margin-bottom: 10px;
    }

    .report-title {
      font-size: 24px;
      font-weight: bold;
      color: #000;
      margin-top: 15px;
    }

    /* Report Info */
    .report-info {
      margin-bottom: 30px;
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .report-info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
    }

    .info-section h6 {
      font-weight: bold;
      margin-bottom: 10px;
      color: #495057;
      border-bottom: 1px solid #dee2e6;
      padding-bottom: 5px;
    }

    .info-item {
      margin-bottom: 6px;
      font-size: 13px;
    }

    .info-label {
      font-weight: bold;
      display: inline-block;
      width: 100px;
    }

    /* Table Styles */
    .report-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
      font-size: 11px;
    }

    .report-table th {
      background-color: #f8f9fa;
      border: 1px solid #000;
      padding: 8px 6px;
      text-align: center;
      font-weight: bold;
      font-size: 10px;
    }

    .report-table td {
      border: 1px solid #000;
      padding: 6px;
      text-align: center;
      font-size: 10px;
    }

    .report-table tbody tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .text-left {
      text-align: left !important;
    }

    .text-right {
      text-align: right !important;
    }

    /* Summary Section */
    .report-summary {
      margin-top: 30px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .summary-card {
      padding: 15px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
    }

    .summary-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #495057;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 14px;
    }

    .summary-total {
      border-top: 2px solid #495057;
      padding-top: 8px;
      margin-top: 8px;
      font-weight: bold;
    }

    /* Print Styles */
    @media print {
      body {
        padding: 20px;
        font-size: 11px;
      }
      
      .no-print {
        display: none !important;
      }
      
      .report-table {
        font-size: 9px;
      }
      
      .report-table th, .report-table td {
        padding: 4px 3px;
        font-size: 9px;
      }
      
      .company-name {
        font-size: 24px;
      }
      
      .report-title {
        font-size: 20px;
      }

      .report-info-grid {
        grid-template-columns: 1fr 1fr;
      }
    }

    /* Print Button */
    .print-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }

    .btn {
      margin-left: 10px;
    }
  </style>
</head>
<body>

  <!-- Print Controls -->
  <div class="print-controls no-print">
    <button class="btn btn-primary" onclick="window.print()">🖨️ Print Report</button>
    <button class="btn btn-secondary" onclick="window.close()">✕ Close</button>
  </div>

  <div class="report-container">
    <!-- Company Header -->
    <div class="company-header" id="companyHeader">
      <div class="company-name">Hzins International Pvt Ltd</div>
      <div class="company-address">
        123 Business District, Karachi, Pakistan<br>
        Phone: +92-21-1234567 | Email: <EMAIL>
      </div>
      <div class="report-title">Product Wise Sale Report</div>
    </div>

    <!-- Report Information -->
    <div class="report-info">
      <div class="report-info-grid">
        <div class="info-section">
          <h6>Filter Information</h6>
          <div class="info-item">
            <span class="info-label">Salesman:</span>
            <span id="reportSalesman">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Town:</span>
            <span id="reportTown">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Zone:</span>
            <span id="reportZone">-</span>
          </div>
        </div>
        <div class="info-section">
          <h6>Business Information</h6>
          <div class="info-item">
            <span class="info-label">Customer:</span>
            <span id="reportCustomer">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Company:</span>
            <span id="reportCompany">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Warehouse:</span>
            <span id="reportWarehouse">-</span>
          </div>
        </div>
        <div class="info-section">
          <h6>Report Details</h6>
          <div class="info-item">
            <span class="info-label">From Date:</span>
            <span id="reportFromDate">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">To Date:</span>
            <span id="reportToDate">-</span>
          </div>
          <div class="info-item">
            <span class="info-label">Generated:</span>
            <span id="reportDate">-</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Table -->
    <table class="report-table">
      <thead>
        <tr>
          <th style="width: 5%;">S.No</th>
          <th style="width: 10%;">Product Code</th>
          <th style="width: 20%;">Product Name</th>
          <th style="width: 15%;">Customer</th>
          <th style="width: 12%;">Salesman</th>
          <th style="width: 8%;">Qty</th>
          <th style="width: 12%;">Unit Price</th>
          <th style="width: 12%;">Total Amount</th>
          <th style="width: 6%;">Date</th>
        </tr>
      </thead>
      <tbody id="reportTableBody">
        <!-- Data will be populated by JavaScript -->
      </tbody>
    </table>

    <!-- Report Summary -->
    <div class="report-summary">
      <div class="summary-card">
        <div class="summary-title">Quantity Summary</div>
        <div class="summary-item">
          <span>Total Products Sold:</span>
          <span id="totalProducts">0</span>
        </div>
        <div class="summary-item">
          <span>Total Quantity:</span>
          <span id="totalQuantity">0</span>
        </div>
        <div class="summary-item">
          <span>Average Qty per Sale:</span>
          <span id="avgQuantity">0</span>
        </div>
      </div>
      <div class="summary-card">
        <div class="summary-title">Financial Summary</div>
        <div class="summary-item">
          <span>Total Sales Amount:</span>
          <span id="totalAmount">Rs. 0</span>
        </div>
        <div class="summary-item">
          <span>Average Sale Value:</span>
          <span id="avgSaleValue">Rs. 0</span>
        </div>
        <div class="summary-item summary-total">
          <span>Grand Total:</span>
          <span id="grandTotal">Rs. 0</span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
      <p>This is a computer generated report. No signature required.</p>
      <p>Generated on <span id="footerDate"></span> | Page 1 of 1</p>
    </div>
  </div>

  <script>
    // Sample sale data (same as main page)
    const sampleSaleData = [
      { productCode: 'ITM001', productName: 'Wireless Mouse', customer: 'ABC Traders', salesman: 'Ahmed Ali', qty: 10, unitPrice: 1500, saleDate: '2024-01-15' },
      { productCode: 'ITM002', productName: 'Keyboard', customer: 'XYZ Enterprises', salesman: 'Muhammad Hassan', qty: 5, unitPrice: 2500, saleDate: '2024-01-16' },
      { productCode: 'ITM003', productName: 'Monitor 24"', customer: 'Global Solutions', salesman: 'Fatima Khan', qty: 2, unitPrice: 25000, saleDate: '2024-01-17' },
      { productCode: 'ITM001', productName: 'Wireless Mouse', customer: 'Tech World', salesman: 'Ali Raza', qty: 15, unitPrice: 1500, saleDate: '2024-01-18' },
      { productCode: 'ITM004', productName: 'USB Cable', customer: 'Mega Store', salesman: 'Sara Ahmed', qty: 50, unitPrice: 300, saleDate: '2024-01-19' },
      { productCode: 'ITM005', productName: 'Hard Drive 1TB', customer: 'Prime Electronics', salesman: 'Usman Malik', qty: 8, unitPrice: 8000, saleDate: '2024-01-20' },
      { productCode: 'ITM002', productName: 'Keyboard', customer: 'ABC Traders', salesman: 'Ayesha Shah', qty: 12, unitPrice: 2500, saleDate: '2024-01-21' },
      { productCode: 'ITM003', productName: 'Monitor 24"', customer: 'XYZ Enterprises', salesman: 'Ahmed Ali', qty: 3, unitPrice: 25000, saleDate: '2024-01-22' }
    ];

    // Get URL parameters
    function getUrlParams() {
      const params = new URLSearchParams(window.location.search);
      return {
        salesman: params.get('salesman') || '',
        town: params.get('town') || '',
        zone: params.get('zone') || '',
        customer: params.get('customer') || '',
        company: params.get('company') || '',
        warehouse: params.get('warehouse') || '',
        fromDate: params.get('fromDate') || '',
        toDate: params.get('toDate') || '',
        showHeading: params.get('showHeading') === 'true',
        print: params.get('print') === 'true'
      };
    }

    // Initialize report
    function initializeReport() {
      const params = getUrlParams();
      
      // Set current date
      const currentDate = new Date().toLocaleDateString('en-GB');
      document.getElementById('reportDate').textContent = currentDate;
      document.getElementById('footerDate').textContent = currentDate;
      
      // Set filter information
      const optionLabels = {
        // Salesman
        'ahmed_ali': 'Ahmed Ali',
        'muhammad_hassan': 'Muhammad Hassan',
        'fatima_khan': 'Fatima Khan',
        'ali_raza': 'Ali Raza',
        'sara_ahmed': 'Sara Ahmed',
        'usman_malik': 'Usman Malik',
        'ayesha_shah': 'Ayesha Shah',
        
        // Towns
        'karachi': 'Karachi',
        'lahore': 'Lahore',
        'islamabad': 'Islamabad',
        'faisalabad': 'Faisalabad',
        'rawalpindi': 'Rawalpindi',
        'multan': 'Multan',
        'peshawar': 'Peshawar',
        'quetta': 'Quetta',
        
        // Zones
        'north': 'North Zone',
        'south': 'South Zone',
        'east': 'East Zone',
        'west': 'West Zone',
        'central': 'Central Zone',
        
        // Customers
        'abc_traders': 'ABC Traders',
        'xyz_enterprises': 'XYZ Enterprises',
        'global_solutions': 'Global Solutions',
        'tech_world': 'Tech World',
        'mega_store': 'Mega Store',
        'prime_electronics': 'Prime Electronics',
        
        // Companies
        'hzins_intl': 'Hzins International Pvt Ltd',
        'alpha_dist': 'Alpha Distributors',
        'beta_corp': 'Beta Corporation',
        'gamma_ltd': 'Gamma Limited',
        
        // Warehouses
        'main_warehouse': 'Main Warehouse',
        'karachi_wh': 'Karachi Warehouse',
        'lahore_wh': 'Lahore Warehouse',
        'islamabad_wh': 'Islamabad Warehouse',
        'faisalabad_wh': 'Faisalabad Warehouse'
      };
      
      document.getElementById('reportSalesman').textContent = optionLabels[params.salesman] || params.salesman || 'All';
      document.getElementById('reportTown').textContent = optionLabels[params.town] || params.town || 'All';
      document.getElementById('reportZone').textContent = optionLabels[params.zone] || params.zone || 'All';
      document.getElementById('reportCustomer').textContent = optionLabels[params.customer] || params.customer || 'All';
      document.getElementById('reportCompany').textContent = optionLabels[params.company] || params.company || 'All';
      document.getElementById('reportWarehouse').textContent = optionLabels[params.warehouse] || params.warehouse || 'All';
      
      // Set date range
      document.getElementById('reportFromDate').textContent = params.fromDate ? new Date(params.fromDate).toLocaleDateString('en-GB') : '-';
      document.getElementById('reportToDate').textContent = params.toDate ? new Date(params.toDate).toLocaleDateString('en-GB') : '-';
      
      // Show/hide company header
      if (!params.showHeading) {
        document.getElementById('companyHeader').style.display = 'none';
      }
      
      // Populate table
      populateTable();
      
      // Auto print if requested
      if (params.print) {
        setTimeout(() => {
          window.print();
        }, 1000);
      }
    }

    // Populate table with data
    function populateTable() {
      const tableBody = document.getElementById('reportTableBody');
      let totalQuantity = 0;
      let totalAmount = 0;
      let totalProducts = sampleSaleData.length;
      
      sampleSaleData.forEach((sale, index) => {
        const row = document.createElement('tr');
        const amount = sale.qty * sale.unitPrice;
        
        totalQuantity += sale.qty;
        totalAmount += amount;
        
        row.innerHTML = `
          <td>${index + 1}</td>
          <td>${sale.productCode}</td>
          <td class="text-left">${sale.productName}</td>
          <td class="text-left">${sale.customer}</td>
          <td class="text-left">${sale.salesman}</td>
          <td>${sale.qty}</td>
          <td class="text-right">Rs. ${sale.unitPrice.toLocaleString()}</td>
          <td class="text-right">Rs. ${amount.toLocaleString()}</td>
          <td>${new Date(sale.saleDate).toLocaleDateString('en-GB')}</td>
        `;
        
        tableBody.appendChild(row);
      });
      
      // Calculate averages
      const avgQuantity = Math.round(totalQuantity / totalProducts);
      const avgSaleValue = Math.round(totalAmount / totalProducts);
      
      // Update summary
      document.getElementById('totalProducts').textContent = totalProducts;
      document.getElementById('totalQuantity').textContent = totalQuantity;
      document.getElementById('avgQuantity').textContent = avgQuantity;
      document.getElementById('totalAmount').textContent = 'Rs. ' + totalAmount.toLocaleString();
      document.getElementById('avgSaleValue').textContent = 'Rs. ' + avgSaleValue.toLocaleString();
      document.getElementById('grandTotal').textContent = 'Rs. ' + totalAmount.toLocaleString();
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', initializeReport);
  </script>

</body>
</html>
