<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sale & Purchase Report</title>
  <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet">
  <style>
    .hidden {
      display: none;
    }
    .card {
      border: none;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
    }
    .card-title {
      color: #2c3e50;
      font-weight: 600;
    }
    .btn-group-toggle .btn {
      border-color: #ced4da;
      color: #2c3e50;
    }
    .btn-group-toggle .btn.active {
      background-color: #007bff;
      color: white;
      border-color: #007bff;
    }
    .form-control {
      border-radius: 0.25rem;
      border-color: #ced4da;
    }
    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      padding: 0.5rem 1.5rem;
      font-weight: 500;
    }
    .btn-primary:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }
    .custom-switch-description {
      color: #2c3e50;
      margin-left: 0.5rem;
    }
    .print-section {
      margin-top: 2rem;
    }
    @media (max-width: 768px) {
      .row {
        flex-direction: column;
      }
      .col-md-4 {
        width: 100%;
        margin-bottom: 1rem;
      }
    }
  </style>
</head>
<body>
  <div class="container mt-5">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h3 class="card-title">Sale & Purchase Report</h3>
        <label class="custom-switch">
          <input type="checkbox" class="custom-switch-input" id="showHeading" checked>
          <span class="custom-switch-indicator"></span>
          <span class="custom-switch-description">Show Company Heading</span>
        </label>
      </div>
      <div class="card-body p-4">
        <div class="form-group mb-4">
          <div class="btn-group btn-group-toggle" data-toggle="buttons">
            <label class="btn btn-outline-primary">
              <input type="radio" name="reportType" value="sale" autocomplete="off"> Sale
            </label>
            <label class="btn btn-outline-primary">
              <input type="radio" name="reportType" value="purchase" autocomplete="off" checked> Purchase
            </label>
            <label class="btn btn-outline-primary">
              <input type="radio" name="reportType" value="saleReturn" autocomplete="off"> Sale Return
            </label>
            <label class="btn btn-outline-primary">
              <input type="radio" name="reportType" value="purchaseReturn" autocomplete="off"> Purchase Return
            </label>
          </div>
        </div>

        <!-- Sale Fields -->
        <div id="saleFields" class="hidden">
          <div class="row">
            <div class="col-md-4 form-group">
              <label class="form-label">Select Salesman:</label>
              <select class="form-control">
                <option>Select Salesman</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Town:</label>
              <select class="form-control">
                <option>Select Town</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Zone:</label>
              <select class="form-control">
                <option>Select Zone</option>
              </select>
            </div>
          </div>
          <div class="row">
            <div class="col-md-4 form-group">
              <label class="form-label">Select Customer:</label>
              <select class="form-control">
                <option>Select Customer</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Company:</label>
              <select class="form-control">
                <option>Select Company</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Warehouse:</label>
              <select class="form-control">
                <option>Select Warehouse</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Purchase Fields -->
        <div id="purchaseFields">
          <div class="row">
            <div class="col-md-4 form-group">
              <label class="form-label">Select Vendor:</label>
              <select class="form-control">
                <option>Select Vendor</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Warehouse:</label>
              <select class="form-control">
                <option>Select Warehouse</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Sale Return Fields -->
        <div id="saleReturnFields" class="hidden">
          <div class="row">
            <div class="col-md-4 form-group">
              <label class="form-label">Select Customer:</label>
              <select class="form-control">
                <option>Select Customer</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Company:</label>
              <select class="form-control">
                <option>Select Company</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Warehouse:</label>
              <select class="form-control">
                <option>Select Warehouse</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Purchase Return Fields -->
        <div id="purchaseReturnFields" class="hidden">
          <div class="row">
            <div class="col-md-4 form-group">
              <label class="form-label">Select Vendor:</label>
              <select class="form-control">
                <option>Select Vendor</option>
              </select>
            </div>
            <div class="col-md-4 form-group">
              <label class="form-label">Select Warehouse:</label>
              <select class="form-control">
                <option>Select Warehouse</option>
              </select>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-4 form-group">
            <label class="form-label">From Date:</label>
            <input type="date" class="form-control">
          </div>
          <div class="col-md-4 form-group">
            <label class="form-label">To Date:</label>
            <input type="date" class="form-control">
          </div>
        </div>
        <div class="print-section">
          <button class="btn btn-primary" onclick="window.print()">Generate Report</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
  <script>
    const reportTypeRadios = document.querySelectorAll('input[name="reportType"]');
    const saleFields = document.getElementById('saleFields');
    const purchaseFields = document.getElementById('purchaseFields');
    const saleReturnFields = document.getElementById('saleReturnFields');
    purchaseReturnFields = document.getElementById('purchaseReturnFields');

    reportTypeRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        saleFields.classList.add('hidden');
        purchaseFields.classList.add('hidden');
        saleReturnFields.classList.add('hidden');
        purchaseReturnFields.classList.add('hidden');

        if (radio.value === 'sale') {
          saleFields.classList.remove('hidden');
        } else if (radio.value === 'purchase') {
          purchaseFields.classList.remove('hidden');
        } else if (radio.value === 'saleReturn') {
          saleReturnFields.classList.remove('hidden');
        } else if (radio.value === 'purchaseReturn') {
          purchaseReturnFields.classList.remove('hidden');
        }
      });
    });
  </script>
</body>
</html>