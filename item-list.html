<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Item List - ERP Style</title>

  <!-- Tabler CSS -->
  <link href="https://unpkg.com/@tabler/core@latest/dist/css/tabler.min.css" rel="stylesheet" />

  <!-- Tom Select for searchable dropdowns -->
  <link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet" />

  <!-- Inter Font -->
  <link href="https://rsms.me/inter/inter.css" rel="stylesheet">

  <style>
    /* ===== Custom ERP-style Inputs ===== */
    @import url('https://rsms.me/inter/inter.css');

    input.form-control, select.form-select {
        border: none;
        border-bottom: 1px solid black;
        border-radius: 0;
        outline: none;
        box-shadow: none;
        font-size: 14px;
    }

    body {
        overflow-x: hidden;
        background-color: #f0f2f5;
        padding: 40px;
        font-family: 'Inter', sans-serif;
    }

    input.form-control:focus, select.form-select:focus {
        border: none;
        border-bottom: 1px solid black;
        outline: none;
        box-shadow: none;
    }

    label {
        font-size: 10px;
        width: 150px;
    }

    .form-label {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .form-control[readonly] {
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .table th, .table td {
        vertical-align: middle;
        text-align: center;
    }

    .card {
      padding: 25px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.06);
    }

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2c3e50;
    }

    hr {
      margin: 25px 0;
    }

    .form-check {
      margin-top: 8px;
      margin-bottom: 8px;
    }

    .form-check-input {
      margin-right: 8px;
    }

    .form-check-label {
      font-size: 14px;
      font-weight: 400;
    }

    /* Price Type Section Styling */
    .price-type-section {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .price-type-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 15px;
      color: #495057;
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    .radio-item {
      display: flex;
      align-items: center;
    }

    .radio-item input[type="radio"] {
      margin-right: 8px;
      transform: scale(1.1);
    }

    .radio-item label {
      font-size: 14px;
      font-weight: 400;
      width: auto;
      margin: 0;
      cursor: pointer;
    }

    /* Tom Select fix */
    .ts-wrapper.single .ts-control {
        border: none !important;
        border-bottom: 1px solid black !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        font-size: 14px;
    }

    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      border-radius: 8px;
      padding: 10px 30px;
      font-weight: 500;
    }

    .btn-primary:hover {
      background-color: #0056b3;
      border-color: #0056b3;
    }

    /* Item List Table */
    .item-list-table {
      margin-top: 30px;
    }

    .table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
      background-color: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      font-weight: 600;
      color: #495057;
    }
  </style>
</head>
<body>

  <div class="container-xl">
    <div class="card shadow-sm">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="section-title">Item List</div>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="showHeading" checked>
          <label class="form-check-label" for="showHeading">Show Company Heading</label>
        </div>
      </div>
      <hr>

      <form>
        <!-- Branch Selection -->
        <div class="row mb-4">
          <div class="col-md-6">
            <label class="form-label">Select Branch</label>
            <select class="form-select searchable" id="branch">
              <option value="">Select Branch</option>
              <option value="main">Main Branch</option>
              <option value="karachi">Karachi Branch</option>
              <option value="lahore">Lahore Branch</option>
              <option value="islamabad">Islamabad Branch</option>
              <option value="faisalabad">Faisalabad Branch</option>
              <option value="multan">Multan Branch</option>
              <option value="peshawar">Peshawar Branch</option>
              <option value="quetta">Quetta Branch</option>
            </select>
          </div>
        </div>

        <!-- Price Type Selection -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="price-type-section">
              <div class="price-type-title">Select Price Type</div>
              <div class="radio-group">
                <div class="radio-item">
                  <input type="radio" id="netRate" name="priceType" value="net_rate" checked>
                  <label for="netRate">Net Rate</label>
                </div>
                <div class="radio-item">
                  <input type="radio" id="tradePrice" name="priceType" value="trade_price">
                  <label for="tradePrice">Trade Price</label>
                </div>
                <div class="radio-item">
                  <input type="radio" id="distributionPrice" name="priceType" value="distribution_price">
                  <label for="distributionPrice">Distribution Price</label>
                </div>
                <div class="radio-item">
                  <input type="radio" id="allPrices" name="priceType" value="all">
                  <label for="allPrices">All</label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Generate Report Button -->
        <div class="row mb-4">
          <div class="col-12">
            <button type="button" class="btn btn-primary" onclick="generateItemList()">
              Generate Item List
            </button>
          </div>
        </div>
      </form>

      <!-- Item List Table -->
      <div class="item-list-table" id="itemListTable" style="display: none;">
        <h4 class="mb-3">Item List Results</h4>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Item Code</th>
                <th>Item Name</th>
                <th>Category</th>
                <th>Unit</th>
                <th id="priceHeader">Net Rate</th>
                <th>Stock Qty</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody id="itemTableBody">
              <!-- Sample data will be populated here -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- JS Libraries -->
  <script src="https://unpkg.com/@tabler/core@latest/dist/js/tabler.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
  <script>
    // Enable searchable selects
    document.querySelectorAll('.searchable').forEach((el) => {
      new TomSelect(el, {
        create: false,
        sortField: {
          field: "text",
          direction: "asc"
        }
      });
    });

    // Sample item data
    const sampleItems = [
      { code: 'ITM001', name: 'Wireless Mouse', category: 'Electronics', unit: 'PCS', netRate: 1500, tradePrice: 1200, distributionPrice: 1000, stock: 50, status: 'Active' },
      { code: 'ITM002', name: 'Keyboard', category: 'Electronics', unit: 'PCS', netRate: 2500, tradePrice: 2000, distributionPrice: 1800, stock: 30, status: 'Active' },
      { code: 'ITM003', name: 'Monitor 24"', category: 'Electronics', unit: 'PCS', netRate: 25000, tradePrice: 22000, distributionPrice: 20000, stock: 15, status: 'Active' },
      { code: 'ITM004', name: 'USB Cable', category: 'Accessories', unit: 'PCS', netRate: 300, tradePrice: 250, distributionPrice: 200, stock: 100, status: 'Active' },
      { code: 'ITM005', name: 'Hard Drive 1TB', category: 'Storage', unit: 'PCS', netRate: 8000, tradePrice: 7200, distributionPrice: 6500, stock: 25, status: 'Active' }
    ];

    // Generate item list function
    function generateItemList() {
      const branch = document.getElementById('branch').value;
      const priceType = document.querySelector('input[name="priceType"]:checked').value;
      
      if (!branch) {
        alert('Please select a branch first');
        return;
      }

      // Update price header based on selection
      const priceHeader = document.getElementById('priceHeader');
      const priceTypeLabels = {
        'net_rate': 'Net Rate',
        'trade_price': 'Trade Price', 
        'distribution_price': 'Distribution Price',
        'all': 'Price'
      };
      priceHeader.textContent = priceTypeLabels[priceType];

      // Populate table
      const tableBody = document.getElementById('itemTableBody');
      tableBody.innerHTML = '';

      sampleItems.forEach(item => {
        const row = document.createElement('tr');
        
        let priceDisplay = '';
        if (priceType === 'all') {
          priceDisplay = `N: ${item.netRate} | T: ${item.tradePrice} | D: ${item.distributionPrice}`;
        } else {
          const priceMap = {
            'net_rate': item.netRate,
            'trade_price': item.tradePrice,
            'distribution_price': item.distributionPrice
          };
          priceDisplay = priceMap[priceType];
        }

        row.innerHTML = `
          <td>${item.code}</td>
          <td>${item.name}</td>
          <td>${item.category}</td>
          <td>${item.unit}</td>
          <td>${priceDisplay}</td>
          <td>${item.stock}</td>
          <td><span class="badge bg-success">${item.status}</span></td>
        `;
        
        tableBody.appendChild(row);
      });

      // Show the table
      document.getElementById('itemListTable').style.display = 'block';
    }

    // Handle price type change to update header
    document.querySelectorAll('input[name="priceType"]').forEach(radio => {
      radio.addEventListener('change', function() {
        if (document.getElementById('itemListTable').style.display !== 'none') {
          generateItemList(); // Regenerate if table is already shown
        }
      });
    });
  </script>

</body>
</html>
